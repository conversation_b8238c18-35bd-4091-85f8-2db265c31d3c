# 测评报告Word导出功能更新说明

## 更新概述

本次更新重构了测评报告的Word导出功能，使生成的Word文档与网页报告保持一致的排版样式，并优化了图表的保存和管理机制。

## 主要改进

### 1. 数据结构优化
- 为图表添加了排序和分组信息
- 支持图表与因子层级的精确对应
- 改进了图表文件的命名和存储机制

### 2. Word生成重构
- 使用Apache POI直接生成Word文档
- 实现了与网页报告一致的布局结构
- 支持动态的因子层级结构
- 优化了图表插入逻辑

### 3. API升级
- 新增 `/measuringroom/testing/save_report_charts_v2` 接口
- 新增 `/export/test_report_word_v2` 接口
- 保持向后兼容性

## 部署步骤

### 1. 数据库更新
执行以下SQL脚本更新数据库结构：

```sql
-- 添加新字段
ALTER TABLE psycloud_test_record_charts 
ADD COLUMN chart_order INT DEFAULT 0 COMMENT '图表顺序',
ADD COLUMN factor_type VARCHAR(50) COMMENT '因子类型标识',
ADD COLUMN chart_type VARCHAR(50) COMMENT '图表类型',
ADD COLUMN chart_index INT DEFAULT 0 COMMENT '图表在同组中的索引';

-- 为现有数据设置默认值
UPDATE psycloud_test_record_charts 
SET chart_order = 0, 
    factor_type = 'unknown', 
    chart_type = 'default', 
    chart_index = 0 
WHERE chart_order IS NULL;

-- 添加索引
CREATE INDEX idx_test_record_charts_order ON psycloud_test_record_charts(record_id, chart_order, chart_index);
```

### 2. 代码部署
- 部署更新后的代码
- 重启应用服务

### 3. 功能测试
1. 访问测评报告页面
2. 点击"下载报告[word]"按钮
3. 验证生成的Word文档格式和内容

## 新功能特点

### 1. 排版一致性
- Word报告与网页报告保持相同的布局结构
- 基本信息以表格形式展示
- 因子层级结构清晰

### 2. 图表准确性
- 图表按正确顺序显示
- 图表与对应的因子内容匹配
- 支持多种图表类型

### 3. 动态适应性
- 支持不同量表的个性化报告结构
- 自动适应因子层级关系
- 灵活的内容组织

## 兼容性说明

- 新版本API与旧版本并存
- 现有功能不受影响
- 可以逐步迁移到新版本

## 故障排除

### 常见问题

1. **图表不显示**
   - 检查图表文件是否存在于 `/charts/` 目录
   - 确认图表保存接口调用成功

2. **Word格式异常**
   - 检查Apache POI依赖是否正确
   - 确认文件写入权限

3. **下载失败**
   - 检查服务器日志
   - 确认文件目录权限

### 日志查看
- 查看应用日志中的 `TestReportWordServiceImpl` 相关信息
- 检查图表保存相关的错误信息

## 后续优化计划

1. **样式增强**
   - 添加更多Word样式选项
   - 支持自定义模板

2. **性能优化**
   - 优化大量图表的处理速度
   - 实现异步生成机制

3. **功能扩展**
   - 支持批量导出
   - 添加导出格式选项（PDF等）

## 技术支持

如遇到问题，请联系开发团队并提供：
- 错误日志信息
- 测评记录ID
- 复现步骤
