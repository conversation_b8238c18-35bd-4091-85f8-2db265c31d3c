package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.hutool.core.date.DateUtil;
import cn.psycloud.psyplatform.config.WordTemplateConfig;
import cn.psycloud.psyplatform.dto.measuringroom.FactorExplainHierarchyDto;
import cn.psycloud.psyplatform.dto.measuringroom.ReportDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestRecordDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordChartsEntity;
import cn.psycloud.psyplatform.service.measuringroom.TestRecordService;
import cn.psycloud.psyplatform.service.measuringroom.TestReportWordService;
import cn.psycloud.psyplatform.util.CommonHelper;
import cn.psycloud.psyplatform.util.WordStyleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblWidth;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STTblWidth;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 测评报告Word生成服务实现类
 */
@Service
@Slf4j
public class TestReportWordServiceImpl implements TestReportWordService {

    @Autowired
    private TestRecordService testRecordService;

    @Autowired
    private WordTemplateConfig wordTemplateConfig;

    @Value("${file.location}")
    private String uploadPath;

    /**
     * 生成与网页一致的Word报告
     * @param recordId 测评记录ID
     * @param folderName 文件夹名称
     * @return 生成的文件路径
     */
    @Override
    public String generateWordReport(Integer recordId, String folderName) {
        log.info("开始生成Word报告，recordId: {}, folderName: {}", recordId, folderName);

        try {
            // 获取报告数据
            ReportDto reportData = testRecordService.getReport(recordId);
            if (reportData == null || reportData.getTestRecord() == null) {
                log.error("无法获取测评报告数据，recordId: {}", recordId);
                return "";
            }

            log.info("成功获取报告数据，因子数量: {}, 图表数量: {}",
                reportData.getListExplains() != null ? reportData.getListExplains().size() : 0,
                reportData.getListCharts() != null ? reportData.getListCharts().size() : 0);



            // 创建Word文档
            XWPFDocument document = new XWPFDocument();

            // 添加报告标题
            addReportTitle(document, reportData.getTestRecord());
            log.debug("添加报告标题完成");

            // 添加基本信息区域
            addBasicInfoSection(document, reportData.getTestRecord());
            log.debug("添加基本信息完成");

            // 添加因子层级结构
            addFactorHierarchySection(document, reportData.getListExplains(), reportData.getListCharts());
            log.debug("添加因子层级结构完成");

            // 保存文档
            String fileName = generateFileName(reportData.getTestRecord());
            String filePath = String.format("report/%s/%s", folderName, fileName);
            String fileAbsPath = uploadPath + filePath;

            // 确保目录存在
            File directory = new File(uploadPath + "report/" + folderName);
            if (!directory.exists()) {
                boolean created = directory.mkdirs();
                log.debug("创建目录: {}, 结果: {}", directory.getAbsolutePath(), created);
            }

            // 写入文件
            try (FileOutputStream out = new FileOutputStream(fileAbsPath)) {
                document.write(out);
            }
            document.close();

            log.info("Word报告生成成功: {}, 文件大小: {} bytes", filePath, new File(fileAbsPath).length());
            return filePath;

        } catch (Exception e) {
            log.error("生成Word报告失败，recordId: {}", recordId, e);
            return "";
        }
    }

    /**
     * 生成Word报告并直接下载
     * @param response HTTP响应
     * @param request HTTP请求
     * @param recordId 测评记录ID
     * @return 生成的文件路径
     */
    @Override
    public String generateAndDownloadWordReport(HttpServletResponse response, HttpServletRequest request, Integer recordId) {
        String folderName = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");
        return generateWordReport(recordId, folderName);
    }

    /**
     * 添加报告标题
     */
    private void addReportTitle(XWPFDocument document, TestRecordDto testRecord) {
        WordTemplateConfig.TitleStyle titleStyle = wordTemplateConfig.getTitle();

        // 添加标题段落
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        WordStyleUtil.applyParagraphSpacing(titleParagraph, titleStyle.getSpacingBefore(), titleStyle.getSpacingAfter());

        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText("《" + testRecord.getScale().getScaleName() + "》测评报告");
        WordStyleUtil.applyTitleStyle(titleRun, titleStyle);

        // 添加副标题
        XWPFParagraph subtitleParagraph = document.createParagraph();
        subtitleParagraph.setAlignment(ParagraphAlignment.CENTER);
        subtitleParagraph.setSpacingAfter(800);

        XWPFRun subtitleRun = subtitleParagraph.createRun();
        subtitleRun.setText("心理测评分析报告");
        subtitleRun.setFontSize(14);
        subtitleRun.setFontFamily(titleStyle.getFontFamily());
        subtitleRun.setColor("6c757d");
        subtitleRun.setItalic(true);
    }

    /**
     * 添加基本信息区域
     */
    private void addBasicInfoSection(XWPFDocument document, TestRecordDto testRecord) {
        // 添加基本信息标题（模仿网页版样式）
        XWPFParagraph sectionTitle = document.createParagraph();
        sectionTitle.setSpacingBefore(400);
        sectionTitle.setSpacingAfter(400);

        XWPFRun iconRun = sectionTitle.createRun();
        iconRun.setText("📊 ");
        iconRun.setFontSize(18);

        XWPFRun titleRun = sectionTitle.createRun();
        titleRun.setText("基本信息");
        titleRun.setBold(true);
        titleRun.setFontSize(18);
        titleRun.setFontFamily("微软雅黑");
        titleRun.setColor("727cf5");

        // 创建卡片式基本信息布局
        addBasicInfoCards(document, testRecord);

        // 添加段落间距
        document.createParagraph().setSpacingAfter(600);
    }

    /**
     * 添加卡片式基本信息
     */
    private void addBasicInfoCards(XWPFDocument document, TestRecordDto testRecord) {
        // 准备数据
        String userName = testRecord.getUser().getRealName() != null && !testRecord.getUser().getRealName().isEmpty()
            ? testRecord.getUser().getRealName() : testRecord.getUser().getLoginName();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String testDate = sdf.format(testRecord.getStartTime());
        String costTime = formatSeconds(testRecord.getTimeInterval());

        // 创建信息卡片表格（2行3列，模仿网页版布局）
        XWPFTable cardTable = document.createTable(2, 3);

        // 设置表格样式
        WordStyleUtil.applyTableStyle(cardTable, wordTemplateConfig.getTable());

        // 设置表格宽度
        cardTable.setWidth("100%");

        // 第一行：姓名、测试日期、所属组织
        addInfoCard(cardTable, 0, 0, "姓名", userName, "727cf5");
        addInfoCard(cardTable, 0, 1, "测试日期", testDate, "e83e8c");
        addInfoCard(cardTable, 0, 2, "所属组织", testRecord.getUser().getStructName(), "20c997");

        // 第二行：耗时、测试项目、空白
        addInfoCard(cardTable, 1, 0, "耗时", costTime, "fd7e14");
        addInfoCard(cardTable, 1, 1, "测试项目", testRecord.getScale().getScaleName(), "6f42c1");
        addInfoCard(cardTable, 1, 2, "", "", "");
    }

    /**
     * 添加单个信息卡片
     */
    private void addInfoCard(XWPFTable table, int row, int col, String label, String value, String borderColor) {
        if (label.isEmpty()) return;

        XWPFTableCell cell = table.getRow(row).getCell(col);

        // 设置单元格样式
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        cell.setColor("f8f9fa");

        // 清除默认段落
        cell.removeParagraph(0);

        // 添加标签
        XWPFParagraph labelPara = cell.addParagraph();
        labelPara.setAlignment(ParagraphAlignment.CENTER);
        labelPara.setSpacingAfter(100);

        XWPFRun labelRun = labelPara.createRun();
        labelRun.setText(label);
        labelRun.setFontFamily("微软雅黑");
        labelRun.setFontSize(10);
        labelRun.setColor("6c757d");

        // 添加值
        XWPFParagraph valuePara = cell.addParagraph();
        valuePara.setAlignment(ParagraphAlignment.CENTER);

        XWPFRun valueRun = valuePara.createRun();
        valueRun.setText(value);
        valueRun.setFontFamily("微软雅黑");
        valueRun.setFontSize(14);
        valueRun.setBold(true);
        valueRun.setColor("2c3e50");
    }

    /**
     * 添加因子层级结构
     */
    private void addFactorHierarchySection(XWPFDocument document, List<FactorExplainHierarchyDto> factors, List<TestRecordChartsEntity> charts) {
        if (factors == null || factors.isEmpty()) {
            return;
        }

        // 添加结果解释标题（模仿网页版样式）
        XWPFParagraph sectionTitle = document.createParagraph();
        sectionTitle.setSpacingBefore(600);
        sectionTitle.setSpacingAfter(400);

        XWPFRun iconRun = sectionTitle.createRun();
        iconRun.setText("💡 ");
        iconRun.setFontSize(18);

        XWPFRun titleRun = sectionTitle.createRun();
        titleRun.setText("结果解释及建议");
        titleRun.setBold(true);
        titleRun.setFontSize(18);
        titleRun.setFontFamily("微软雅黑");
        titleRun.setColor("727cf5");

        // 按照前端相同的逻辑组织因子数据
        OrganizedFactorData organizedData = organizeFactorData(factors);

        // 按照前端相同的顺序显示因子
        // 1. 首先显示顶层因子（有子因子的因子）- 仅显示标题和解释，不显示子因子
        if (!organizedData.topLevelFactors.isEmpty()) {
            for (FactorExplainHierarchyDto topLevelFactor : organizedData.topLevelFactors) {
                addTopLevelFactorSection(document, topLevelFactor, charts);
            }
            // 在所有顶层因子显示完后，插入顶层图表
            insertTopLevelFactorCharts(document, charts);
        }

        // 2. 然后显示父因子组（显示子因子的详细内容）
        for (ParentGroup parentGroup : organizedData.parentGroups) {
            addParentGroupSection(document, parentGroup, charts);
        }

        // 3. 最后显示独立因子
        if (!organizedData.independentFactors.isEmpty()) {
            WordTemplateConfig.FactorStyle factorStyle = wordTemplateConfig.getFactor();

            // 为独立因子添加一个统一的标题
            XWPFParagraph independentTitle = document.createParagraph();
            independentTitle.setSpacingAfter(factorStyle.getSpacingAfter());
            XWPFRun independentTitleRun = independentTitle.createRun();
            independentTitleRun.setText("结果解释及建议");
            WordStyleUtil.applyFactorTitleStyle(independentTitleRun, factorStyle, 2);

            // 插入独立因子的图表
            insertIndependentFactorCharts(document, charts);

            for (FactorExplainHierarchyDto independentFactor : organizedData.independentFactors) {
                addFactorSection(document, independentFactor, charts, 2);
            }
        }
    }

    /**
     * 添加顶层因子部分（仅显示标题和解释，不显示子因子）
     */
    private void addTopLevelFactorSection(XWPFDocument document, FactorExplainHierarchyDto factor, List<TestRecordChartsEntity> charts) {
        WordTemplateConfig.FactorStyle factorStyle = wordTemplateConfig.getFactor();

        // 添加因子标题（带装饰线）
        XWPFParagraph factorTitle = document.createParagraph();
        factorTitle.setSpacingBefore(500);
        factorTitle.setSpacingAfter(300);

        // 添加装饰符号
        XWPFRun decorRun = factorTitle.createRun();
        decorRun.setText("▶ ");
        decorRun.setFontSize(16);
        decorRun.setColor("727cf5");
        decorRun.setBold(true);

        XWPFRun titleRun = factorTitle.createRun();
        titleRun.setText(factor.getFactorName());
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        titleRun.setFontFamily("微软雅黑");
        titleRun.setColor("2c3e50");

        // 添加因子解释（带背景色效果）
        if (factor.getInterpretation() != null && !factor.getInterpretation().trim().isEmpty()) {
            XWPFParagraph interpretation = document.createParagraph();
            interpretation.setSpacingAfter(400);
            interpretation.setIndentationLeft(400);
            interpretation.setIndentationRight(200);

            XWPFRun interpretationRun = interpretation.createRun();
            interpretationRun.setText(factor.getInterpretation());
            interpretationRun.setFontSize(12);
            interpretationRun.setFontFamily("微软雅黑");
            interpretationRun.setColor("495057");
        }

        // 顶层因子的图表将在所有顶层因子显示完后统一插入
    }

    /**
     * 插入顶层因子的图表
     */
    private void insertTopLevelFactorCharts(XWPFDocument document, List<TestRecordChartsEntity> charts) {
        if (charts == null || charts.isEmpty()) {
            return;
        }

        // 获取并排序顶层因子的图表
        List<TestRecordChartsEntity> topLevelCharts = charts.stream()
            .filter(chart -> chart.getFactorType() != null && chart.getFactorType().equals("topLevel"))
            .sorted((c1, c2) -> {
                // 按chartOrder和chartIndex排序
                int orderCompare = Integer.compare(
                    c1.getChartOrder() != null ? c1.getChartOrder() : 0,
                    c2.getChartOrder() != null ? c2.getChartOrder() : 0
                );
                if (orderCompare != 0) return orderCompare;

                return Integer.compare(
                    c1.getChartIndex() != null ? c1.getChartIndex() : 0,
                    c2.getChartIndex() != null ? c2.getChartIndex() : 0
                );
            })
            .collect(java.util.stream.Collectors.toList());

        for (TestRecordChartsEntity chart : topLevelCharts) {
            insertChart(document, chart);
        }
    }

    /**
     * 插入独立因子的图表
     */
    private void insertIndependentFactorCharts(XWPFDocument document, List<TestRecordChartsEntity> charts) {
        if (charts == null || charts.isEmpty()) {
            return;
        }

        // 获取并排序独立因子的图表
        List<TestRecordChartsEntity> independentCharts = charts.stream()
            .filter(chart -> chart.getFactorType() != null && chart.getFactorType().equals("independent"))
            .sorted((c1, c2) -> {
                int orderCompare = Integer.compare(
                    c1.getChartOrder() != null ? c1.getChartOrder() : 0,
                    c2.getChartOrder() != null ? c2.getChartOrder() : 0
                );
                if (orderCompare != 0) return orderCompare;

                return Integer.compare(
                    c1.getChartIndex() != null ? c1.getChartIndex() : 0,
                    c2.getChartIndex() != null ? c2.getChartIndex() : 0
                );
            })
            .collect(java.util.stream.Collectors.toList());

        for (TestRecordChartsEntity chart : independentCharts) {
            insertChart(document, chart);
        }
    }

    /**
     * 添加因子部分
     */
    private void addFactorSection(XWPFDocument document, FactorExplainHierarchyDto factor, List<TestRecordChartsEntity> charts, int level) {
        WordTemplateConfig.FactorStyle factorStyle = wordTemplateConfig.getFactor();

        // 根据层级设置缩进
        int indentLevel = (level - 1) * factorStyle.getIndentUnit();

        // 添加因子标题
        XWPFParagraph factorTitle = document.createParagraph();
        factorTitle.setSpacingAfter(factorStyle.getSpacingAfter());
        factorTitle.setIndentationLeft(indentLevel);
        XWPFRun titleRun = factorTitle.createRun();
        titleRun.setText(factor.getFactorName());
        WordStyleUtil.applyFactorTitleStyle(titleRun, factorStyle, level);

        // 添加因子解释
        if (factor.getInterpretation() != null && !factor.getInterpretation().trim().isEmpty()) {
            XWPFParagraph interpretation = document.createParagraph();
            interpretation.setSpacingAfter(factorStyle.getSpacingAfter());
            interpretation.setIndentationLeft(indentLevel + 200); // 解释内容额外缩进
            XWPFRun interpretationRun = interpretation.createRun();
            interpretationRun.setText(factor.getInterpretation());
            WordStyleUtil.applyFactorContentStyle(interpretationRun, factorStyle);
        }

        // 插入相关图表（仅为叶子节点插入图表）
        if (factor.getChildren() == null || factor.getChildren().isEmpty()) {
            insertFactorCharts(document, factor, charts);
        }

        // 处理子因子（在新的组织结构中，这部分可能不会被调用）
        if (factor.getChildren() != null && !factor.getChildren().isEmpty()) {
            for (FactorExplainHierarchyDto child : factor.getChildren()) {
                addFactorSection(document, child, charts, level + 1);
            }
        }
    }

    /**
     * 插入因子相关的图表（仅用于子因子）
     */
    private void insertFactorCharts(XWPFDocument document, FactorExplainHierarchyDto factor, List<TestRecordChartsEntity> charts) {
        if (charts == null || charts.isEmpty()) {
            return;
        }

        // 对于子因子，通常不单独插入图表，因为图表是按组显示的
        // 这个方法主要用于特殊情况下的因子图表匹配
        for (TestRecordChartsEntity chart : charts) {
            if (chart.getFactorType() != null) {
                String factorType = chart.getFactorType();
                // 只匹配包含具体因子ID的图表
                if (factorType.contains(factor.getFactorId().toString()) &&
                    !factorType.equals("topLevel") &&
                    !factorType.equals("independent") &&
                    !factorType.startsWith("parent_")) {
                    insertChart(document, chart);
                }
            }
        }
    }

    /**
     * 插入图表
     */
    private void insertChart(XWPFDocument document, TestRecordChartsEntity chart) {
        try {
            // 确保路径正确拼接
            String chartPath = uploadPath;
            if (!chartPath.endsWith("/") && !chartPath.endsWith("\\")) {
                chartPath += "/";
            }
            chartPath += "charts/" + chart.getChartsImg();

            File chartFile = new File(chartPath);

            if (chartFile.exists()) {

                // 插入图表
                XWPFParagraph chartParagraph = document.createParagraph();
                chartParagraph.setAlignment(ParagraphAlignment.CENTER);
                chartParagraph.setSpacingBefore(200);
                chartParagraph.setSpacingAfter(300);

                XWPFRun chartRun = chartParagraph.createRun();
                try (FileInputStream chartStream = new FileInputStream(chartFile)) {
                    // 根据图表类型调整大小
                    WordStyleUtil.ChartSize chartSize = WordStyleUtil.getChartSize(
                        chart.getChartType() != null ? chart.getChartType() : "default",
                        wordTemplateConfig.getChart()
                    );

                    chartRun.addPicture(chartStream, XWPFDocument.PICTURE_TYPE_PNG, chart.getChartsImg(),
                        Units.toEMU(chartSize.getWidth()), Units.toEMU(chartSize.getHeight()));
                }
            } else {
                log.warn("图表文件不存在: {}, 完整路径: {}", chart.getChartsImg(), chartFile.getAbsolutePath());

                // 添加占位符文本
                XWPFParagraph placeholder = document.createParagraph();
                placeholder.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun placeholderRun = placeholder.createRun();
                placeholderRun.setText("[图表文件不存在: " + chart.getChartsImg() + "]");
                placeholderRun.setColor("ff0000");
                placeholderRun.setItalic(true);
            }
        } catch (Exception e) {
            log.error("插入图表失败: {}", chart.getChartsImg(), e);

            // 添加错误占位符
            try {
                XWPFParagraph errorParagraph = document.createParagraph();
                errorParagraph.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun errorRun = errorParagraph.createRun();
                errorRun.setText("[图表插入失败: " + e.getMessage() + "]");
                errorRun.setColor("ff0000");
                errorRun.setItalic(true);
            } catch (Exception ex) {
                log.error("添加错误占位符失败", ex);
            }
        }
    }

    /**
     * 获取图表标题
     */
    private String getChartTitle(String chartType) {
        switch (chartType) {
            case "column":
                return "柱状图";
            case "line":
                return "折线图";
            case "pie":
                return "饼图";
            case "bar":
                return "条形图";
            default:
                return "图表";
        }
    }





    /**
     * 设置表格单元格内容
     */
    private void setTableCell(XWPFTable table, int row, int col, String label, String value) {
        XWPFTableRow tableRow = table.getRow(row);
        WordTemplateConfig.TableStyle tableStyle = wordTemplateConfig.getTable();

        // 设置标签
        XWPFTableCell labelCell = tableRow.getCell(col);
        labelCell.setText(label);
        WordStyleUtil.applyTableCellStyle(labelCell, tableStyle, true);

        // 设置值
        if (col + 1 < tableRow.getTableCells().size()) {
            XWPFTableCell valueCell = tableRow.getCell(col + 1);
            valueCell.setText(value);
            WordStyleUtil.applyTableCellStyle(valueCell, tableStyle, false);
        }
    }



    /**
     * 生成文件名
     */
    private String generateFileName(TestRecordDto testRecord) {
        String userName = testRecord.getUser().getRealName() != null && !testRecord.getUser().getRealName().isEmpty() 
            ? testRecord.getUser().getRealName() : testRecord.getUser().getLoginName();
        return testRecord.getId() + "_" + userName + "_" + testRecord.getScale().getScaleName() + ".docx";
    }

    /**
     * 格式化秒数为时间字符串
     */
    private String formatSeconds(Integer seconds) {
        if (seconds == null || seconds <= 0) {
            return "0分0秒";
        }

        int minutes = seconds / 60;
        int remainingSeconds = seconds % 60;

        if (minutes > 0) {
            return minutes + "分" + remainingSeconds + "秒";
        } else {
            return remainingSeconds + "秒";
        }
    }

    /**
     * 按照前端相同的逻辑组织因子数据
     */
    private OrganizedFactorData organizeFactorData(List<FactorExplainHierarchyDto> factors) {
        OrganizedFactorData result = new OrganizedFactorData();

        for (FactorExplainHierarchyDto factor : factors) {
            if (factor.getChildren() != null && !factor.getChildren().isEmpty()) {
                // 有子因子的顶层因子
                result.topLevelFactors.add(factor);

                // 添加到父因子组
                ParentGroup parentGroup = new ParentGroup();
                parentGroup.parentId = factor.getFactorId();
                parentGroup.parentName = factor.getFactorName();
                parentGroup.children = factor.getChildren();
                result.parentGroups.add(parentGroup);
            } else {
                // 独立因子
                result.independentFactors.add(factor);
            }
        }

        return result;
    }

    /**
     * 添加父因子组部分
     */
    private void addParentGroupSection(XWPFDocument document, ParentGroup parentGroup, List<TestRecordChartsEntity> charts) {
        WordTemplateConfig.FactorStyle factorStyle = wordTemplateConfig.getFactor();

        // 添加父因子组标题
        XWPFParagraph groupTitle = document.createParagraph();
        groupTitle.setSpacingAfter(factorStyle.getSpacingAfter());
        XWPFRun titleRun = groupTitle.createRun();
        titleRun.setText(parentGroup.parentName);
        WordStyleUtil.applyFactorTitleStyle(titleRun, factorStyle, 2);

        // 插入父因子组的图表
        insertParentGroupCharts(document, parentGroup, charts);

        // 处理子因子
        for (FactorExplainHierarchyDto child : parentGroup.children) {
            addFactorSection(document, child, charts, 2);
        }
    }

    /**
     * 插入父因子组的图表
     */
    private void insertParentGroupCharts(XWPFDocument document, ParentGroup parentGroup, List<TestRecordChartsEntity> charts) {
        if (charts == null || charts.isEmpty()) {
            return;
        }

        String expectedFactorType = "parent_" + parentGroup.parentId;

        // 获取并排序父因子组的图表
        List<TestRecordChartsEntity> parentGroupCharts = charts.stream()
            .filter(chart -> chart.getFactorType() != null && chart.getFactorType().equals(expectedFactorType))
            .sorted((c1, c2) -> {
                int orderCompare = Integer.compare(
                    c1.getChartOrder() != null ? c1.getChartOrder() : 0,
                    c2.getChartOrder() != null ? c2.getChartOrder() : 0
                );
                if (orderCompare != 0) return orderCompare;

                return Integer.compare(
                    c1.getChartIndex() != null ? c1.getChartIndex() : 0,
                    c2.getChartIndex() != null ? c2.getChartIndex() : 0
                );
            })
            .collect(java.util.stream.Collectors.toList());

        for (TestRecordChartsEntity chart : parentGroupCharts) {
            insertChart(document, chart);
        }
    }

    /**
     * 组织后的因子数据结构
     */
    private static class OrganizedFactorData {
        List<FactorExplainHierarchyDto> topLevelFactors = new ArrayList<>();
        List<ParentGroup> parentGroups = new ArrayList<>();
        List<FactorExplainHierarchyDto> independentFactors = new ArrayList<>();
    }

    /**
     * 父因子组
     */
    private static class ParentGroup {
        Integer parentId;
        String parentName;
        List<FactorExplainHierarchyDto> children;
    }
}
