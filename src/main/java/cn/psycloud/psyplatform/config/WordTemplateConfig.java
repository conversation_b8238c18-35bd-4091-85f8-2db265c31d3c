package cn.psycloud.psyplatform.config;

import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * Word模板配置类
 * 集中管理Word文档生成的所有样式配置
 * 如需修改样式，请直接修改此类中的默认值
 */
@Component
@Data
public class WordTemplateConfig {
    
    /**
     * 标题样式配置
     */
    private TitleStyle title = new TitleStyle();
    
    /**
     * 表格样式配置
     */
    private TableStyle table = new TableStyle();
    
    /**
     * 图表样式配置
     */
    private ChartStyle chart = new ChartStyle();
    
    /**
     * 因子样式配置
     */
    private FactorStyle factor = new FactorStyle();
    
    @Data
    public static class TitleStyle {
        private String fontFamily = "微软雅黑";
        private int fontSize = 22;
        private String color = "2c3e50";
        private int spacingBefore = 200;
        private int spacingAfter = 600;
    }
    
    @Data
    public static class TableStyle {
        private String fontFamily = "微软雅黑";
        private int fontSize = 12;
        private String labelColor = "2c3e50";
        private String valueColor = "495057";
        private String backgroundColor = "f8f9fa";
        private int cellPadding = 100;
    }
    
    @Data
    public static class ChartStyle {
        private int defaultWidth = 450;
        private int defaultHeight = 300;
        private int columnWidth = 500;
        private int columnHeight = 320;
        private int lineWidth = 480;
        private int lineHeight = 300;
        private int pieWidth = 350;
        private int pieHeight = 350;
        private int spacingAfter = 400;
    }
    
    @Data
    public static class FactorStyle {
        private String fontFamily = "微软雅黑";
        private int topLevelFontSize = 16;
        private int parentGroupFontSize = 14;
        private int childFontSize = 12;
        private String titleColor = "2c3e50";
        private String contentColor = "495057";
        private int indentUnit = 400;
        private int spacingBefore = 300;
        private int spacingAfter = 200;
    }
}
