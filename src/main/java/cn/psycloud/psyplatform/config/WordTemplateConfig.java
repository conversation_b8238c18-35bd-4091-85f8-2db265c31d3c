package cn.psycloud.psyplatform.config;

import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * Word模板配置类
 * 集中管理Word文档生成的所有样式配置
 * 如需修改样式，请直接修改此类中的默认值
 */
@Component
@Data
public class WordTemplateConfig {
    
    /**
     * 标题样式配置
     */
    private TitleStyle title = new TitleStyle();
    
    /**
     * 表格样式配置
     */
    private TableStyle table = new TableStyle();
    
    /**
     * 图表样式配置
     */
    private ChartStyle chart = new ChartStyle();
    
    /**
     * 因子样式配置
     */
    private FactorStyle factor = new FactorStyle();
    
    @Data
    public static class TitleStyle {
        private String fontFamily = "微软雅黑";
        private int fontSize = 24;
        private String color = "2c3e50";
        private String subtitleColor = "6c757d";
        private int spacingBefore = 400;
        private int spacingAfter = 800;
        private String backgroundColor = "f8f9ff";
        private String borderColor = "727cf5";
    }
    
    @Data
    public static class TableStyle {
        private String fontFamily = "微软雅黑";
        private int fontSize = 12;
        private String labelColor = "6c757d";
        private String valueColor = "2c3e50";
        private String backgroundColor = "f8f9fa";
        private String borderColor = "e8ecf4";
        private String headerBackgroundColor = "f1f3ff";
        private int cellPadding = 150;
        private int borderWidth = 1;
    }
    
    @Data
    public static class ChartStyle {
        private int defaultWidth = 450;
        private int defaultHeight = 300;
        private int columnWidth = 500;
        private int columnHeight = 320;
        private int lineWidth = 480;
        private int lineHeight = 300;
        private int pieWidth = 350;
        private int pieHeight = 350;
        private int spacingAfter = 400;
    }
    
    @Data
    public static class FactorStyle {
        private String fontFamily = "微软雅黑";
        private int topLevelFontSize = 18;
        private int parentGroupFontSize = 16;
        private int childFontSize = 14;
        private String titleColor = "2c3e50";
        private String contentColor = "495057";
        private String accentColor = "727cf5";
        private String backgroundColor = "f8f9ff";
        private String borderColor = "e8ecf4";
        private int indentUnit = 400;
        private int spacingBefore = 500;
        private int spacingAfter = 300;
        private int contentIndent = 400;
    }
}
