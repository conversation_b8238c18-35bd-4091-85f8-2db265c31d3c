package cn.psycloud.psyplatform.controller.measuringroom;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.measuringroom.*;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordChartsEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordEntity;
import cn.psycloud.psyplatform.service.measuringroom.*;
import com.alibaba.excel.EasyExcel;
import lombok.SneakyThrows;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  测评管理
 */
@Controller
@RequestMapping("/measuringroom/testing")
public class TestingController {
    @Autowired
    private TestRecordService testRecordService;
    @Autowired
    private ScaleTypeService scaleTypeService;
    @Autowired
    private ScaleService scaleService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private TestResultService testResultService;
    @Autowired
    private  TestScoreService testScoreService;
    @Autowired
    private ImportTestResultService importTestResultService;

    /**
     *  心理测评首页
     * @return 视图
     */
    @GetMapping("/index")
    public ModelAndView index() {
        var mv = new ModelAndView();
        var listScaleTypes = scaleTypeService.getList(new ScaleTypeDto());
        mv.addObject("scaleTypes",listScaleTypes);
        mv.setViewName("measuringroom/testing/index");
        return mv;
    }

    /**
     *  量表测试引导页：查询量表信息
     * @return 视图
     */
    @GetMapping("/guide")
    public ModelAndView guide(@RequestParam Integer scaleId) {
        var mv = new ModelAndView();
        var scale = scaleService.getById(scaleId);
        mv.addObject("scale",scale);
        mv.setViewName("measuringroom/testing/guide");
        return mv;
    }

    /**
     *  判断任务中的量表是否完成
     * @param taskId 测评任务Id
     * @param scaleId 量表Id
     * @return 是否完成
     */
    @RequestMapping("/is_scale_done")
    @ResponseBody
    public Object isScaleDone(@RequestParam Integer taskId, @RequestParam Integer scaleId) {
        return taskService.isScaleDone(taskId, scaleId);
    }

    /**
     *  更新测评开始时间
     * @param recordId 测评记录id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update_starttime",method= RequestMethod.POST)
    @ResponseBody
    public Object UpdateStartTime(@RequestParam Integer recordId){
        var result = new JsonResult<>();
        var testRecord = testRecordService.getById(recordId);
        if(testRecordService.isUserInfoComplete(testRecord)){
            if(testRecordService.updateTestStartTime(recordId) > 0) {
                result.setResultCode(ResultCodeAndMsg.SuccessCode);
                result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
            }
            else {
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg(ResultCodeAndMsg.FailureMsg);
            }
        }
        else {
            result.setResultCode(ResultCodeAndMsg.ScaleUserInfoValidFailedCode);
            result.setResultMsg(ResultCodeAndMsg.ScaleUserInfoValidFailedMsg);
        }
        return result;
    }

    /**
     *  测评过程
     * @return 视图
     */
    @GetMapping("/do_test")
    public String doTest(HttpServletRequest request) {
        request.setAttribute("embu",ScaleIDDto.EMBU);
        request.setAttribute("aqy",ScaleIDDto.YDL_aqy);
        request.setAttribute("yys",ScaleIDDto.BKYYS);
        request.setAttribute("mbtis",ScaleIDDto.MBTIS);
        return "measuringroom/testing/doTest";
    }

    /**
     *  初始化测评
     * @param recordId 测评记录id
     * @return 测评记录实体对象
     */
    @RequestMapping(value = "/init",method= RequestMethod.POST)
    @ResponseBody
    public Object doTest(@RequestParam Integer recordId) {
        return JSONUtil.toJsonStr(testRecordService.getById(recordId));
    }

    /**
     *  保存测评记录
     * @param dto 测评记录实体对象
     * @param request 请求
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add_record",method = RequestMethod.POST)
    @ResponseBody
    public Object AddRecord(@RequestBody ScaleDto dto, HttpServletRequest request){
        var result = new JsonResult<>();
        var testRecordDto = new TestRecordDto();
        testRecordDto.setScale(dto);
        var user = (UserDto)request.getSession().getAttribute("user");
        testRecordDto.setUserId(user.getUserId());
        if(testRecordService.isUserInfoComplete(testRecordDto)) {
            var testRecordEntity = new TestRecordEntity();
            testRecordEntity.setUserId(user.getUserId());
            testRecordEntity.setScaleId(testRecordDto.getScale().getId());
            testRecordEntity.setState(0);
            testRecordEntity.setStartTime(DateUtil.date());
            testRecordService.addRecord(testRecordEntity);
            var recordId = testRecordEntity.getId();
            if(recordId >0) {
                result.setResultCode(ResultCodeAndMsg.SuccessCode);
                result.setResultMsg(recordId.toString());
            }
            else {
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg(ResultCodeAndMsg.FailureMsg);
            }
        }
        else {
            result.setResultCode(ResultCodeAndMsg.ScaleUserInfoValidFailedCode);
            result.setResultMsg(ResultCodeAndMsg.ScaleUserInfoValidFailedMsg);
        }
        return result;
    }

    @GetMapping("/calc")
    public String calc(){
        return "measuringroom/testing/calc";
    }

    @RequestMapping(value = "/calc", method = RequestMethod.POST)
    @ResponseBody
    public Object calc(@RequestParam Integer recordId){
        var result = new JsonResult<>();
        //var testRecord = testRecordService.getById(recordId);
        //var scaleId = testRecord.getScale().getId();

        /*var recordIds = new ArrayList<Integer>();
        for(int i = 1667; i <= 1766; i++){
            recordIds.add(i);
        }
        var scaleId = 10000003;
        for(Integer recordId2 : recordIds){
            testScoreService.calc(recordId2,scaleId);
        }*/
        //testScoreService.calc(recordId,scaleId);
        result.setResultCode(ResultCodeAndMsg.SuccessCode);
        result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        return result;
    }

    /**
     *  保存答案
     * @param vo 答案实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/save_result",method=RequestMethod.POST)
    @ResponseBody
    public Object SaveResult(@RequestBody TestResultVO vo){
        var result = new JsonResult<>();
        //保存答案
        if(testResultService.saveResult(vo)){
            //计算得分
            testScoreService.calc(vo.getRecordId(),vo.getScaleId());
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  保存答案（EMBU问卷）
     * @return 结果实体对象
     */
    @RequestMapping(value = "/save_result_embu",method=RequestMethod.POST)
    @ResponseBody
    public Object SaveResultEMBU(@RequestBody TestResultVO vo){
        var result = new JsonResult<>();
        //保存答案
        if(testResultService.saveResultEMBU(vo)){
            //计算得分
            testScoreService.calc(vo.getRecordId(),vo.getScaleId());
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  测试报告页面
     * @param recordId 测评记录id
     * @return 视图
     */
    @GetMapping("/report")
    public ModelAndView getReport(@RequestParam Integer recordId) {
        var mv =new ModelAndView();
        var testRecordDto = testRecordService.getById(recordId);
        mv.addObject("testRecord", testRecordDto);
        mv.addObject("apmAndNineHouse", ScaleIDDto.APMAndNineHouse);
        mv.addObject("sds",ScaleIDDto.SDS);
        mv.addObject("pcm",ScaleIDDto.PCM);
        mv.addObject("epqc",ScaleIDDto.EPQC);
        mv.addObject("epqa",ScaleIDDto.EPQA);
        mv.addObject("zylx",ScaleIDDto.ZYLX);
        mv.addObject("nineHouse",ScaleIDDto.NineHouse);
        mv.addObject("sas",ScaleIDDto.SAS);
        mv.addObject("bdi",ScaleIDDto.BDI);
        mv.addObject("fes",ScaleIDDto.FES);
        mv.setViewName("measuringroom/testing/getReport");
        return mv;
    }

    /**
     *  贝康育婴师问卷报告页面
     * @return 视图
     */
    @GetMapping("/report_yys")
    public String getReport_yys() {
        return "measuringroom/testing/getReport_yys";
    }

    /**
     *  安全员问卷报告页面
     * @return 视图
     */
    @GetMapping("/report_aqy")
    public String getReport_aqy() {
        return "measuringroom/testing/getReport_aqy";
    }

    /**
     *  测试报告
     * @param recordId  测评记录Id
     * @param request 请求
     * @return 结果实体
     */
    @RequestMapping("/report")
    @ResponseBody
    public Object getReport(@RequestParam Integer recordId, HttpServletRequest request) {
        var result = new JsonResult<ReportDto>();
        var reportDto = testRecordService.getReport(recordId);
        if(reportDto != null){
            result.setData(reportDto);
            var user = (UserDto)request.getSession().getAttribute("user");
            var roleId = user.getRole().getRoleId();
            if (roleId == 3){
                Integer resultViewRule = taskService.getResultViewRuleByRecordId(recordId);
                if(resultViewRule == null){
                    result.setResultCode(ResultCodeAndMsg.SuccessCode);
                }
                else if(resultViewRule ==3 || (resultViewRule == 1 && testRecordService.isAbnormal(recordId) > 0)){
                    result.setResultCode(ResultCodeAndMsg.FailureCode);
                }
                else
                    result.setResultCode(ResultCodeAndMsg.SuccessCode);
            }
        }
        return JSONUtil.toJsonStr(result);
    }

    /**
     *  保存测评报告图表
     * @param dto 测评报告图表实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/save_report_charts",method=RequestMethod.POST)
    @ResponseBody
    public Object insert(@RequestBody TestRecordChartsDto dto){
        var result = new JsonResult<>();
        if(testRecordService.saveTestRecordCharts(dto)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  保存测评报告图表（新版本，支持详细图表信息）
     * @param requestDto 测评报告图表请求实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/save_report_charts_v2",method=RequestMethod.POST)
    @ResponseBody
    public Object saveChartsV2(@RequestBody TestRecordChartsRequestDto requestDto){
        var result = new JsonResult<>();
        if(testRecordService.saveTestRecordChartsV2(requestDto)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 获取因子结果解释
     * @param recordId 测评记录id
     * @return 因子结果解释列表
     */
    @GetMapping(value = "/getFactorExplains")
    @ResponseBody
    public Object getFactorExplains(@RequestParam Integer recordId) {
        var result = new JsonResult<>();
        try {
            var explains = testRecordService.getFactorExplains(recordId);
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
            result.setData(explains);
        } catch (Exception e) {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg("获取因子解释失败：" + e.getMessage());
        }
        return result;
    }



    /**
     *  测评结果查看页面
     * @param request 请求
     * @return 视图
     */
    @GetMapping("/record_list")
    public String recordList(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("01040301".equals(a.getFunctionCode()) || "01040301".equals(a.getParentCode())))
                .collect(Collectors.toList());
        boolean canDelete = false, canExport = false, canViewReport = false;
        for (SysFunctionDto privilege: privilegeList){
            //删除权限
            if ("0104030101".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
            //导出权限
            if ("0104030103".equals(privilege.getFunctionCode())) {
                canExport = true;
            }
            //查看报告权限
            if ("0104030104".equals(privilege.getFunctionCode())) {
                canViewReport = true;
            }
        }
        request.setAttribute("canDelete",canDelete);
        request.setAttribute("canExport",canExport);
        request.setAttribute("canViewReport",canViewReport);
        return "measuringroom/testing/recordList";
    }

    /**
     *  获取测评记录集合：分页
     * @param dto 查询条件
     * @return 测评记录集合
     */
    @RequestMapping(value="/record_list",method=RequestMethod.POST)
    @ResponseBody
    public Object recordList(@RequestBody TestRecordDto dto) {
        return testRecordService.getListByPaged(dto);
    }

    /**
     *  获取我的测评记录集合：分页
     * @param dto 查询条件
     * @return 测评记录集合
     */
    @RequestMapping(value="/get_my_records",method=RequestMethod.POST)
    @ResponseBody
    public Object getMyRecords(@RequestBody TestRecordDto dto) {
        return testRecordService.getMyRecords(dto);
    }

    /**
     *  删除测评记录
     * @param ids id集合
     * @return 结果实体对象
     */
    @RequestMapping(value = "/del_record",method=RequestMethod.POST)
    @ResponseBody
    public Object batchDel(@RequestParam String ids){
        var result = new JsonResult<>();
        if(testRecordService.batchDel(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  测评情况筛选
     * @param request 请求
     * @return 视图
     */
    @GetMapping("/filter_record_list")
    public String recordFilterList(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("01040302".equals(a.getFunctionCode()) || "01040302".equals(a.getParentCode())))
                .collect(Collectors.toList());
        boolean  canExport = false;
        for (SysFunctionDto privilege: privilegeList){
            //导出权限
            if ("0104030201".equals(privilege.getFunctionCode())) {
                canExport = true;
            }
        }
        request.setAttribute("canExport",canExport);
        return "measuringroom/testing/recordFilterList";
    }

    /**
     *  测评情况筛选
     * @param dto 筛选条件
     * @return 集合
     */
    @RequestMapping(value = "/get_score_records",method=RequestMethod.POST)
    @ResponseBody
    public Object recordFilterList(@RequestBody TestScoreDto dto) {
        return testScoreService.getTestScoreListByPaged(dto);
    }

    /**
     *  心理异常筛选
     * @param request 请求
     * @return 视图
     */
    @GetMapping("/get_score_records")
    public String getAbnormalList(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("0104030301".equals(a.getFunctionCode())))
                .collect(Collectors.toList());
        boolean  canExport = false;
        for (SysFunctionDto privilege: privilegeList){
            //导出权限
            if ("0104030301".equals(privilege.getFunctionCode())) {
                canExport = true;
            }
        }
        request.setAttribute("canExport",canExport);
        return "measuringroom/testing/getAbnormalList";
    }

    /**
     *  获取九型人格测试结果
     * @return 视图
     */
    @GetMapping("/get_ninehouse_records")
    public String getNineHouseList(){
        return "measuringroom/testing/getNineHouseList";
    }

    /**
     *  获取九型人格测试结果：分页
     * @param dto 查询条件
     * @return 集合
     */
    @RequestMapping(value = "/get_ninehouse_records",method=RequestMethod.POST)
    @ResponseBody
    public Object getNineHouseList(@RequestBody NineHouseStatDto dto){
        return testRecordService.getNineHouseList(dto);
    }

    /**
     *  我的测评记录
     * @return 视图
     */
    @GetMapping("/get_my_records")
    public String getMyRecords(HttpServletRequest request){
        var user = (UserDto)request.getSession().getAttribute("user");
        request.setAttribute("userId",user.getUserId());
        return "measuringroom/testing/myRecords";
    }

    /**
     *  导入测试结果
     * @return 视图
     */
    @GetMapping("/import_testresult")
    public String ImportTestResult(){
        return "measuringroom/testing/importTestResult";
    }

    /**
     *  导入测试结果
     * @param file excel文件
     * @return 结果实体对象
     */
    @SneakyThrows
    @RequestMapping(value = "/import_testresult",method = RequestMethod.POST)
    @ResponseBody
    public Object ImportTestResult(@RequestParam("file") MultipartFile file,
                                   @RequestParam Integer taskId,
                                   @RequestParam Integer scaleId){
        var list = EasyExcel.read(file.getInputStream())
                .sheet()
                .doReadSync();
        var importResult = importTestResultService.importTestResultFromExcel(taskId, scaleId, list);
        
        var result = new JsonResult<>();
        result.setResultCode(ResultCodeAndMsg.SuccessCode);
        result.setResultMsg(String.format("导入完成，成功：%d条，失败：%d条", 
            importResult.getSuccessCount(), importResult.getFailCount()));
        result.setData(importResult);
        
        return result;
    }

    /**
     * 获取测评任务的背景图片信息
     * @param recordId 测评记录ID
     * @return 背景图片信息
     */
    @RequestMapping(value = "/get_background", method = RequestMethod.POST)
    @ResponseBody
    public Object getBackground(@RequestParam Integer recordId) {
        var result = new JsonResult<>();
        try {
            var backgroundInfo = taskService.getBackgroundByRecordId(recordId);
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setData(backgroundInfo);
        } catch (Exception e) {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg("获取背景信息失败");
        }
        return result;
    }
}
