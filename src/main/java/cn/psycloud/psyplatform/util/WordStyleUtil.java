package cn.psycloud.psyplatform.util;

import cn.psycloud.psyplatform.config.WordTemplateConfig;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblWidth;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STTblWidth;

import java.math.BigInteger;

/**
 * Word样式工具类
 */
public class WordStyleUtil {
    
    /**
     * 应用标题样式
     */
    public static void applyTitleStyle(XWPFRun run, WordTemplateConfig.TitleStyle style) {
        run.setFontFamily(style.getFontFamily());
        run.setFontSize(style.getFontSize());
        run.setColor(style.getColor());
        run.setBold(true);
    }

    /**
     * 应用副标题样式
     */
    public static void applySubtitleStyle(XWPFRun run, WordTemplateConfig.TitleStyle style) {
        run.setFontFamily(style.getFontFamily());
        run.setFontSize(14);
        run.setColor(style.getSubtitleColor());
        run.setItalic(true);
    }
    
    /**
     * 应用段落间距
     */
    public static void applyParagraphSpacing(XWPFParagraph paragraph, int spacingBefore, int spacingAfter) {
        paragraph.setSpacingBefore(spacingBefore);
        paragraph.setSpacingAfter(spacingAfter);
    }
    
    /**
     * 应用因子标题样式
     */
    public static void applyFactorTitleStyle(XWPFRun run, WordTemplateConfig.FactorStyle style, int level) {
        run.setFontFamily(style.getFontFamily());
        run.setColor(style.getTitleColor());
        run.setBold(true);
        
        // 根据层级设置字体大小
        switch (level) {
            case 1:
                run.setFontSize(style.getTopLevelFontSize());
                break;
            case 2:
                run.setFontSize(style.getParentGroupFontSize());
                break;
            default:
                run.setFontSize(style.getChildFontSize());
                break;
        }
    }
    
    /**
     * 应用因子内容样式
     */
    public static void applyFactorContentStyle(XWPFRun run, WordTemplateConfig.FactorStyle style) {
        run.setFontFamily(style.getFontFamily());
        run.setFontSize(style.getChildFontSize());
        run.setColor(style.getContentColor());
    }
    
    /**
     * 应用表格样式
     */
    public static void applyTableStyle(XWPFTable table, WordTemplateConfig.TableStyle style) {
        // 设置表格宽度
        CTTblPr tblPr = table.getCTTbl().getTblPr();
        if (tblPr == null) {
            tblPr = table.getCTTbl().addNewTblPr();
        }

        CTTblWidth tblWidth = tblPr.getTblW();
        if (tblWidth == null) {
            tblWidth = tblPr.addNewTblW();
        }
        tblWidth.setType(STTblWidth.DXA);
        tblWidth.setW(BigInteger.valueOf(9000));

        // 设置单元格样式
        for (int i = 0; i < table.getRows().size(); i++) {
            XWPFTableRow row = table.getRow(i);
            for (int j = 0; j < row.getTableCells().size(); j++) {
                XWPFTableCell cell = row.getCell(j);

                // 设置单元格垂直对齐
                cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

                // 设置单元格背景色
                cell.setColor(style.getBackgroundColor());
            }
        }
    }

    /**
     * 创建现代化的信息卡片表格
     */
    public static void applyModernCardTableStyle(XWPFTable table, WordTemplateConfig.TableStyle style) {
        // 设置表格宽度为100%
        CTTblPr tblPr = table.getCTTbl().getTblPr();
        if (tblPr == null) {
            tblPr = table.getCTTbl().addNewTblPr();
        }

        CTTblWidth tblWidth = tblPr.getTblW();
        if (tblWidth == null) {
            tblWidth = tblPr.addNewTblW();
        }
        tblWidth.setType(STTblWidth.PCT);
        tblWidth.setW(BigInteger.valueOf(5000)); // 100% width

        // 设置表格边框
        table.setInsideHBorder(XWPFTable.XWPFBorderType.SINGLE, 1, 0, "e8ecf4");
        table.setInsideVBorder(XWPFTable.XWPFBorderType.SINGLE, 1, 0, "e8ecf4");
        table.setTopBorder(XWPFTable.XWPFBorderType.SINGLE, 1, 0, "e8ecf4");
        table.setBottomBorder(XWPFTable.XWPFBorderType.SINGLE, 1, 0, "e8ecf4");
        table.setLeftBorder(XWPFTable.XWPFBorderType.SINGLE, 1, 0, "e8ecf4");
        table.setRightBorder(XWPFTable.XWPFBorderType.SINGLE, 1, 0, "e8ecf4");
    }
    
    /**
     * 应用表格单元格样式
     */
    public static void applyTableCellStyle(XWPFTableCell cell, WordTemplateConfig.TableStyle style, boolean isLabel) {
        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        paragraph.setSpacingBefore(style.getCellPadding());
        paragraph.setSpacingAfter(style.getCellPadding());

        XWPFRun run = paragraph.getRuns().size() > 0 ? paragraph.getRuns().get(0) : paragraph.createRun();

        run.setFontFamily(style.getFontFamily());
        run.setFontSize(style.getFontSize());

        if (isLabel) {
            run.setBold(true);
            run.setColor(style.getLabelColor());
        } else {
            run.setColor(style.getValueColor());
        }
    }

    /**
     * 创建带装饰的段落标题
     */
    public static void createDecoratedTitle(XWPFParagraph paragraph, String title, String iconText,
                                          WordTemplateConfig.FactorStyle style) {
        paragraph.setSpacingBefore(style.getSpacingBefore());
        paragraph.setSpacingAfter(style.getSpacingAfter());

        // 添加图标
        if (iconText != null && !iconText.isEmpty()) {
            XWPFRun iconRun = paragraph.createRun();
            iconRun.setText(iconText + " ");
            iconRun.setFontSize(style.getTopLevelFontSize());
            iconRun.setColor(style.getAccentColor());
            iconRun.setBold(true);
        }

        // 添加标题文本
        XWPFRun titleRun = paragraph.createRun();
        titleRun.setText(title);
        titleRun.setBold(true);
        titleRun.setFontSize(style.getTopLevelFontSize());
        titleRun.setFontFamily(style.getFontFamily());
        titleRun.setColor(style.getTitleColor());
    }
    
    /**
     * 获取图表尺寸
     */
    public static ChartSize getChartSize(String chartType, WordTemplateConfig.ChartStyle style) {
        switch (chartType) {
            case "column":
            case "bar":
                return new ChartSize(style.getColumnWidth(), style.getColumnHeight());
            case "line":
                return new ChartSize(style.getLineWidth(), style.getLineHeight());
            case "pie":
                return new ChartSize(style.getPieWidth(), style.getPieHeight());
            default:
                return new ChartSize(style.getDefaultWidth(), style.getDefaultHeight());
        }
    }
    
    /**
     * 图表尺寸类
     */
    public static class ChartSize {
        private final int width;
        private final int height;
        
        public ChartSize(int width, int height) {
            this.width = width;
            this.height = height;
        }
        
        public int getWidth() {
            return width;
        }
        
        public int getHeight() {
            return height;
        }
    }
}
